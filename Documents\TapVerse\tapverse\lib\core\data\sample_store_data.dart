import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/store_item.dart';

class SampleStoreData {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Sample store items data
  static final List<Map<String, dynamic>> _sampleItems = [
    // Skins
    {
      'name': 'Golden Ball',
      'type': 'skin',
      'price': 250,
      'assetPath': 'assets/skins/golden_ball.png',
      'description': 'A shiny golden ball that gleams in the light',
      'isAvailable': true,
    },
    {
      'name': 'Rainbow Ball',
      'type': 'skin',
      'price': 500,
      'assetPath': 'assets/skins/rainbow_ball.png',
      'description': 'A colorful ball that changes colors as it moves',
      'isAvailable': true,
    },
    {
      'name': 'Crystal Ball',
      'type': 'skin',
      'price': 750,
      'assetPath': 'assets/skins/crystal_ball.png',
      'description': 'A transparent crystal ball with magical properties',
      'isAvailable': true,
    },
    {
      'name': 'Fire Ball',
      'type': 'skin',
      'price': 400,
      'assetPath': 'assets/skins/fire_ball.png',
      'description': 'A ball engulfed in flames',
      'isAvailable': true,
    },
    {
      'name': 'Ice Ball',
      'type': 'skin',
      'price': 400,
      'assetPath': 'assets/skins/ice_ball.png',
      'description': 'A frozen ball with icy effects',
      'isAvailable': true,
    },

    // Effects
    {
      'name': 'Sparkle Trail',
      'type': 'effect',
      'price': 300,
      'assetPath': 'assets/effects/sparkle_trail.json',
      'description': 'Leaves a trail of sparkles behind moving objects',
      'isAvailable': true,
    },
    {
      'name': 'Lightning Strike',
      'type': 'effect',
      'price': 600,
      'assetPath': 'assets/effects/lightning_strike.json',
      'description': 'Electric lightning effects on impact',
      'isAvailable': true,
    },
    {
      'name': 'Explosion Burst',
      'type': 'effect',
      'price': 450,
      'assetPath': 'assets/effects/explosion_burst.json',
      'description': 'Explosive particle effects on collision',
      'isAvailable': true,
    },
    {
      'name': 'Magic Aura',
      'type': 'effect',
      'price': 350,
      'assetPath': 'assets/effects/magic_aura.json',
      'description': 'Mystical aura surrounding game objects',
      'isAvailable': true,
    },

    // Themes
    {
      'name': 'Neon Theme',
      'type': 'theme',
      'price': 800,
      'assetPath': 'assets/themes/neon_theme.json',
      'description': 'Bright neon colors with glowing effects',
      'isAvailable': true,
    },
    {
      'name': 'Space Theme',
      'type': 'theme',
      'price': 1000,
      'assetPath': 'assets/themes/space_theme.json',
      'description': 'Cosmic background with stars and planets',
      'isAvailable': true,
    },
    {
      'name': 'Ocean Theme',
      'type': 'theme',
      'price': 600,
      'assetPath': 'assets/themes/ocean_theme.json',
      'description': 'Underwater theme with bubbles and sea life',
      'isAvailable': true,
    },
    {
      'name': 'Forest Theme',
      'type': 'theme',
      'price': 550,
      'assetPath': 'assets/themes/forest_theme.json',
      'description': 'Natural forest environment with trees and wildlife',
      'isAvailable': true,
    },

    // Power-ups
    {
      'name': 'Double Score',
      'type': 'powerup',
      'price': 200,
      'assetPath': 'assets/powerups/double_score.png',
      'description': 'Doubles your score for 30 seconds',
      'isAvailable': true,
    },
    {
      'name': 'Time Freeze',
      'type': 'powerup',
      'price': 300,
      'assetPath': 'assets/powerups/time_freeze.png',
      'description': 'Freezes time for 10 seconds',
      'isAvailable': true,
    },
    {
      'name': 'Shield',
      'type': 'powerup',
      'price': 250,
      'assetPath': 'assets/powerups/shield.png',
      'description': 'Protects from one collision',
      'isAvailable': true,
    },
    {
      'name': 'Speed Boost',
      'type': 'powerup',
      'price': 150,
      'assetPath': 'assets/powerups/speed_boost.png',
      'description': 'Increases movement speed for 20 seconds',
      'isAvailable': true,
    },

    // Sounds
    {
      'name': 'Retro Beeps',
      'type': 'sound',
      'price': 100,
      'assetPath': 'assets/sounds/retro_beeps.mp3',
      'description': 'Classic 8-bit sound effects',
      'isAvailable': true,
    },
    {
      'name': 'Orchestral Pack',
      'type': 'sound',
      'price': 400,
      'assetPath': 'assets/sounds/orchestral_pack.mp3',
      'description': 'Epic orchestral music and effects',
      'isAvailable': true,
    },
    {
      'name': 'Nature Sounds',
      'type': 'sound',
      'price': 200,
      'assetPath': 'assets/sounds/nature_sounds.mp3',
      'description': 'Peaceful nature sounds and ambient music',
      'isAvailable': true,
    },
    {
      'name': 'Electronic Beats',
      'type': 'sound',
      'price': 300,
      'assetPath': 'assets/sounds/electronic_beats.mp3',
      'description': 'Modern electronic music and sound effects',
      'isAvailable': true,
    },

    // Free items
    {
      'name': 'Basic Skin',
      'type': 'skin',
      'price': 0,
      'assetPath': 'assets/skins/basic_skin.png',
      'description': 'A simple starter skin for new players',
      'isAvailable': true,
    },
    {
      'name': 'Welcome Effect',
      'type': 'effect',
      'price': 0,
      'assetPath': 'assets/effects/welcome_effect.json',
      'description': 'A simple particle effect for beginners',
      'isAvailable': true,
    },
  ];

  // Initialize sample store data
  static Future<void> initializeSampleData() async {
    try {
      print('Initializing sample store data...');
      
      final batch = _firestore.batch();
      
      for (int i = 0; i < _sampleItems.length; i++) {
        final itemData = _sampleItems[i];
        final docRef = _firestore.collection('store').doc('item_${i + 1}');
        
        batch.set(docRef, {
          ...itemData,
          'createdAt': FieldValue.serverTimestamp(),
        });
      }
      
      await batch.commit();
      print('Sample store data initialized successfully!');
      print('Added ${_sampleItems.length} items to the store.');
      
    } catch (e) {
      print('Error initializing sample store data: $e');
    }
  }

  // Clear all store data (for testing purposes)
  static Future<void> clearStoreData() async {
    try {
      print('Clearing store data...');
      
      final snapshot = await _firestore.collection('store').get();
      final batch = _firestore.batch();
      
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      print('Store data cleared successfully!');
      
    } catch (e) {
      print('Error clearing store data: $e');
    }
  }

  // Add a single item to the store
  static Future<void> addStoreItem(Map<String, dynamic> itemData) async {
    try {
      await _firestore.collection('store').add({
        ...itemData,
        'createdAt': FieldValue.serverTimestamp(),
      });
      print('Added item: ${itemData['name']}');
    } catch (e) {
      print('Error adding store item: $e');
    }
  }

  // Get sample items for preview (without adding to Firestore)
  static List<StoreItem> getSampleItemsPreview() {
    return _sampleItems.asMap().entries.map((entry) {
      final index = entry.key;
      final data = entry.value;
      
      return StoreItem(
        id: 'item_${index + 1}',
        name: data['name'],
        type: StoreItem.parseStoreItemType(data['type']),
        price: data['price'],
        assetPath: data['assetPath'],
        description: data['description'],
        isAvailable: data['isAvailable'],
      );
    }).toList();
  }

  // Check if store has any items
  static Future<bool> hasStoreItems() async {
    try {
      final snapshot = await _firestore.collection('store').limit(1).get();
      return snapshot.docs.isNotEmpty;
    } catch (e) {
      print('Error checking store items: $e');
      return false;
    }
  }

  // Initialize store data if empty
  static Future<void> initializeIfEmpty() async {
    try {
      final hasItems = await hasStoreItems();
      if (!hasItems) {
        print('Store is empty, initializing with sample data...');
        await initializeSampleData();
      } else {
        print('Store already has items, skipping initialization.');
      }
    } catch (e) {
      print('Error checking/initializing store: $e');
    }
  }
}
