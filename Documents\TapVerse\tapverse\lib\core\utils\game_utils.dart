import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Utility class for common game calculations and helpers
class GameUtils {
  GameUtils._();

  /// Calculate distance between two points
  static double distance(Offset point1, Offset point2) {
    final dx = point1.dx - point2.dx;
    final dy = point1.dy - point2.dy;
    return math.sqrt(dx * dx + dy * dy);
  }

  /// Check if two rectangles intersect (collision detection)
  static bool rectsIntersect(Rect rect1, Rect rect2) {
    return rect1.overlaps(rect2);
  }

  /// Check if two circles intersect
  static bool circlesIntersect(Offset center1, double radius1, Offset center2, double radius2) {
    final distance = GameUtils.distance(center1, center2);
    return distance <= (radius1 + radius2);
  }

  /// Check if a point is inside a rectangle
  static bool pointInRect(Offset point, Rect rect) {
    return rect.contains(point);
  }

  /// Check if a point is inside a circle
  static bool pointInCircle(Offset point, Offset center, double radius) {
    return distance(point, center) <= radius;
  }

  /// Normalize an angle to be between 0 and 2π
  static double normalizeAngle(double angle) {
    while (angle < 0) angle += 2 * math.pi;
    while (angle >= 2 * math.pi) angle -= 2 * math.pi;
    return angle;
  }

  /// Convert degrees to radians
  static double degreesToRadians(double degrees) {
    return degrees * math.pi / 180;
  }

  /// Convert radians to degrees
  static double radiansToDegrees(double radians) {
    return radians * 180 / math.pi;
  }

  /// Calculate angle between two points
  static double angleBetweenPoints(Offset from, Offset to) {
    return math.atan2(to.dy - from.dy, to.dx - from.dx);
  }

  /// Move a point in a direction by a distance
  static Offset movePoint(Offset point, double angle, double distance) {
    return Offset(
      point.dx + math.cos(angle) * distance,
      point.dy + math.sin(angle) * distance,
    );
  }

  /// Clamp a value between min and max
  static double clamp(double value, double min, double max) {
    return math.max(min, math.min(max, value));
  }

  /// Clamp a point within bounds
  static Offset clampPoint(Offset point, Size bounds) {
    return Offset(
      clamp(point.dx, 0, bounds.width),
      clamp(point.dy, 0, bounds.height),
    );
  }

  /// Linear interpolation between two values
  static double lerp(double start, double end, double t) {
    return start + (end - start) * t;
  }

  /// Linear interpolation between two points
  static Offset lerpPoint(Offset start, Offset end, double t) {
    return Offset(
      lerp(start.dx, end.dx, t),
      lerp(start.dy, end.dy, t),
    );
  }

  /// Generate a random number between min and max
  static double randomRange(double min, double max) {
    return min + math.Random().nextDouble() * (max - min);
  }

  /// Generate a random integer between min and max (inclusive)
  static int randomIntRange(int min, int max) {
    return min + math.Random().nextInt(max - min + 1);
  }

  /// Generate a random point within bounds
  static Offset randomPoint(Size bounds) {
    return Offset(
      randomRange(0, bounds.width),
      randomRange(0, bounds.height),
    );
  }

  /// Check if a value is approximately equal to another (for floating point comparison)
  static bool approximately(double a, double b, {double epsilon = 0.001}) {
    return (a - b).abs() < epsilon;
  }

  /// Wrap a value around bounds (useful for screen wrapping)
  static double wrap(double value, double min, double max) {
    final range = max - min;
    if (value < min) {
      return max - ((min - value) % range);
    } else if (value >= max) {
      return min + ((value - min) % range);
    }
    return value;
  }

  /// Wrap a point around screen bounds
  static Offset wrapPoint(Offset point, Size bounds) {
    return Offset(
      wrap(point.dx, 0, bounds.width),
      wrap(point.dy, 0, bounds.height),
    );
  }

  /// Calculate velocity from two positions and time delta
  static Offset calculateVelocity(Offset oldPosition, Offset newPosition, Duration deltaTime) {
    final deltaSeconds = deltaTime.inMicroseconds / 1000000.0;
    if (deltaSeconds == 0) return Offset.zero;
    
    return Offset(
      (newPosition.dx - oldPosition.dx) / deltaSeconds,
      (newPosition.dy - oldPosition.dy) / deltaSeconds,
    );
  }

  /// Apply friction to velocity
  static Offset applyFriction(Offset velocity, double friction, Duration deltaTime) {
    final deltaSeconds = deltaTime.inMicroseconds / 1000000.0;
    final frictionFactor = math.pow(friction, deltaSeconds);
    return velocity * frictionFactor.toDouble();
  }

  /// Bounce velocity off a surface (simple reflection)
  static Offset bounceVelocity(Offset velocity, Offset surfaceNormal) {
    final dot = velocity.dx * surfaceNormal.dx + velocity.dy * surfaceNormal.dy;
    return Offset(
      velocity.dx - 2 * dot * surfaceNormal.dx,
      velocity.dy - 2 * dot * surfaceNormal.dy,
    );
  }

  /// Get screen bounds from context
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// Get safe area bounds from context
  static EdgeInsets getSafeArea(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Convert screen coordinates to game coordinates (if using different coordinate systems)
  static Offset screenToGame(Offset screenPoint, Size screenSize, Size gameSize) {
    return Offset(
      (screenPoint.dx / screenSize.width) * gameSize.width,
      (screenPoint.dy / screenSize.height) * gameSize.height,
    );
  }

  /// Convert game coordinates to screen coordinates
  static Offset gameToScreen(Offset gamePoint, Size gameSize, Size screenSize) {
    return Offset(
      (gamePoint.dx / gameSize.width) * screenSize.width,
      (gamePoint.dy / gameSize.height) * screenSize.height,
    );
  }

  /// Calculate score based on various factors
  static int calculateScore({
    required int baseScore,
    required int multiplier,
    required int level,
    required Duration timeBonus,
    int maxTimeBonus = 1000,
  }) {
    final timeBonusScore = math.max(0, maxTimeBonus - timeBonus.inSeconds);
    return (baseScore * multiplier * level) + timeBonusScore;
  }

  /// Format time duration for display
  static String formatTime(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    final milliseconds = (duration.inMilliseconds % 1000) ~/ 10;
    
    if (minutes > 0) {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${seconds.toString().padLeft(2, '0')}.${milliseconds.toString().padLeft(2, '0')}';
    }
  }

  /// Format score for display with commas
  static String formatScore(int score) {
    return score.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  /// Ease in animation curve
  static double easeIn(double t) {
    return t * t;
  }

  /// Ease out animation curve
  static double easeOut(double t) {
    return 1 - (1 - t) * (1 - t);
  }

  /// Ease in-out animation curve
  static double easeInOut(double t) {
    return t < 0.5 ? 2 * t * t : 1 - 2 * (1 - t) * (1 - t);
  }

  /// Smooth step function
  static double smoothStep(double t) {
    return t * t * (3 - 2 * t);
  }

  /// Smoother step function
  static double smootherStep(double t) {
    return t * t * t * (t * (t * 6 - 15) + 10);
  }
}
