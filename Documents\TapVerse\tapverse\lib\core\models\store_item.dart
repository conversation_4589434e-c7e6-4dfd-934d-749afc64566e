import 'package:cloud_firestore/cloud_firestore.dart';

enum StoreItemType {
  skin,
  effect,
  powerup,
  theme,
  sound,
}

class StoreItem {
  final String id;
  final String name;
  final StoreItemType type;
  final int price;
  final String assetPath;
  final String? description;
  final bool isAvailable;

  StoreItem({
    required this.id,
    required this.name,
    required this.type,
    required this.price,
    required this.assetPath,
    this.description,
    this.isAvailable = true,
  });

  factory StoreItem.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return StoreItem(
      id: doc.id,
      name: data['name'] ?? '',
      type: parseStoreItemType(data['type'] ?? 'skin'),
      price: data['price'] ?? 0,
      assetPath: data['assetPath'] ?? '',
      description: data['description'],
      isAvailable: data['isAvailable'] ?? true,
    );
  }

  static StoreItemType parseStoreItemType(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'skin':
        return StoreItemType.skin;
      case 'effect':
        return StoreItemType.effect;
      case 'powerup':
        return StoreItemType.powerup;
      case 'theme':
        return StoreItemType.theme;
      case 'sound':
        return StoreItemType.sound;
      default:
        return StoreItemType.skin;
    }
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'type': type.name,
      'price': price,
      'assetPath': assetPath,
      'description': description,
      'isAvailable': isAvailable,
    };
  }

  StoreItem copyWith({
    String? id,
    String? name,
    StoreItemType? type,
    int? price,
    String? assetPath,
    String? description,
    bool? isAvailable,
  }) {
    return StoreItem(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      price: price ?? this.price,
      assetPath: assetPath ?? this.assetPath,
      description: description ?? this.description,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  String get formattedPrice {
    if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(1)}K';
    } else {
      return price.toString();
    }
  }

  String get typeDisplayName {
    switch (type) {
      case StoreItemType.skin:
        return 'Skin';
      case StoreItemType.effect:
        return 'Effect';
      case StoreItemType.powerup:
        return 'Power-up';
      case StoreItemType.theme:
        return 'Theme';
      case StoreItemType.sound:
        return 'Sound';
    }
  }

  bool get isFree => price == 0;

  @override
  String toString() {
    return 'StoreItem(id: $id, name: $name, type: $type, price: $price, assetPath: $assetPath, description: $description, isAvailable: $isAvailable)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is StoreItem &&
        other.id == id &&
        other.name == name &&
        other.type == type &&
        other.price == price &&
        other.assetPath == assetPath &&
        other.description == description &&
        other.isAvailable == isAvailable;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        type.hashCode ^
        price.hashCode ^
        assetPath.hashCode ^
        description.hashCode ^
        isAvailable.hashCode;
  }
}
